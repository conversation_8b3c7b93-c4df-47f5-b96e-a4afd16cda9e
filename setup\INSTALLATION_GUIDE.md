# 🚀 交互绘本项目安装指南

## 📋 系统要求

- **操作系统**: Windows 10/11, macOS 10.15+, 或 Linux
- **Node.js**: 16.0 或更高版本（推荐 18.x LTS）
- **内存**: 至少 4GB RAM
- **存储**: 至少 1GB 可用空间
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

## 🔧 第一步：安装 Node.js

### Windows 用户

#### 方法1：官方安装包（推荐）
1. 访问 [Node.js 官网](https://nodejs.org/)
2. 下载 LTS 版本（长期支持版本）
3. 运行 `.msi` 安装文件
4. 按照安装向导完成安装
5. 重启命令行工具

#### 方法2：使用包管理器
```powershell
# 使用 Chocolatey
choco install nodejs

# 使用 Winget
winget install OpenJS.NodeJS
```

### macOS 用户

#### 方法1：官方安装包
1. 访问 [Node.js 官网](https://nodejs.org/)
2. 下载 macOS 版本
3. 运行 `.pkg` 安装文件

#### 方法2：使用 Homebrew
```bash
brew install node
```

### Linux 用户

#### Ubuntu/Debian
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### CentOS/RHEL
```bash
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

## ✅ 第二步：验证安装

打开命令行工具，运行以下命令：

```bash
node --version
npm --version
```

应该看到类似输出：
```
v18.17.0
9.6.7
```

## 📦 第三步：安装项目依赖

### 自动安装（推荐）

#### Windows 用户
进入 `setup` 文件夹，双击运行 `install-dependencies.bat` 或在 PowerShell 中运行：
```powershell
cd setup
.\install-dependencies.ps1
```

#### macOS/Linux 用户
```bash
cd setup
chmod +x install-dependencies.sh
./install-dependencies.sh
```

### 手动安装

如果自动安装脚本无法运行，请手动执行以下步骤：

1. **清理旧依赖**（如果存在）：
```bash
rm -rf node_modules
rm package-lock.json
```

2. **安装依赖**：
```bash
npm install
```

3. **验证安装**：
```bash
npm list --depth=0
```

## 🌐 网络问题解决

如果遇到网络问题，可以使用国内镜像：

### 设置淘宝镜像
```bash
npm config set registry https://registry.npmmirror.com
```

### 临时使用镜像
```bash
npm install --registry https://registry.npmmirror.com
```

### 恢复官方镜像
```bash
npm config set registry https://registry.npmjs.org
```

## 🔑 第四步：配置 API 密钥

项目已经预配置了 LIBLIB AI 的测试密钥，但建议您申请自己的密钥：

1. **LIBLIB AI 密钥**：
   - 访问 [LIBLIB AI 官网](https://www.liblibai.cloud/)
   - 注册账号并获取 API 密钥
   - 在根目录的 `.env` 文件中更新密钥

2. **OpenAI 密钥**（可选）：
   - 访问 [OpenAI 平台](https://platform.openai.com/api-keys)
   - 获取 API 密钥
   - 在根目录的 `.env` 文件中添加密钥

## 🚀 第五步：启动项目

### 开发模式
```bash
npm run dev
```

访问 http://localhost:5173

### 生产构建
```bash
npm run build
npm run preview
```

## 🛠️ 可用命令

| 命令 | 描述 |
|------|------|
| `npm run dev` | 启动开发服务器 |
| `npm run build` | 构建生产版本 |
| `npm run preview` | 预览构建结果 |
| `npm run lint` | 运行代码检查 |
| `npm run generate-illustrations` | 生成插画 |
| `npm run test-illustration` | 测试插画生成 |
| `npm run debug-api` | 调试 API 连接 |

## 🔍 故障排除

### 常见问题

#### 1. Node.js 未找到
**错误**: `'node' 不是内部或外部命令`
**解决**: 重新安装 Node.js 并确保添加到 PATH

#### 2. 权限错误
**错误**: `EACCES: permission denied`
**解决**: 
```bash
# macOS/Linux
sudo npm install -g npm

# Windows (以管理员身份运行)
npm install -g npm
```

#### 3. 网络超时
**错误**: `network timeout`
**解决**: 使用国内镜像或检查网络连接

#### 4. 依赖冲突
**错误**: `peer dependency warnings`
**解决**: 
```bash
npm install --legacy-peer-deps
```

#### 5. 端口被占用
**错误**: `Port 5173 is already in use`
**解决**: 
```bash
# 查找占用端口的进程
netstat -ano | findstr :5173

# 或使用不同端口
npm run dev -- --port 3000
```

### 获取帮助

如果遇到其他问题：

1. 检查 [项目文档](../docs/README.md)
2. 查看 [常见问题](../docs/FAQ.md)
3. 提交 [Issue](https://github.com/your-repo/issues)

## 🎉 安装完成

恭喜！您已经成功安装了交互绘本项目。现在可以：

1. 运行 `npm run dev` 启动开发服务器
2. 在浏览器中访问 http://localhost:5173
3. 开始体验小熊波波的友谊冒险！

---

**注意**: 首次启动可能需要几分钟来编译和加载资源，请耐心等待。
