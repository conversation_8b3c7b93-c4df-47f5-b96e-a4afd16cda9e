@echo off
echo ========================================
echo 🚀 交互绘本项目依赖安装脚本
echo ========================================
echo.

echo 📋 检查 Node.js 和 npm 版本...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装或未添加到 PATH
    echo 请先安装 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

npm --version
if %errorlevel% neq 0 (
    echo ❌ npm 未安装或未添加到 PATH
    pause
    exit /b 1
)

echo ✅ Node.js 和 npm 已安装
echo.

echo 📦 清理旧的依赖...
if exist node_modules (
    rmdir /s /q node_modules
    echo ✅ 已清理旧的 node_modules
)

if exist package-lock.json (
    del package-lock.json
    echo ✅ 已清理旧的 package-lock.json
)

echo.
echo 📥 安装项目依赖...
npm install

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    echo 请检查网络连接或尝试使用淘宝镜像:
    echo npm config set registry https://registry.npmmirror.com
    pause
    exit /b 1
)

echo.
echo ✅ 依赖安装完成！
echo.

echo 🔧 验证安装...
npm list --depth=0

echo.
echo 🎉 安装完成！现在您可以运行以下命令：
echo.
echo   启动开发服务器:
echo   npm run dev
echo.
echo   构建生产版本:
echo   npm run build
echo.
echo   运行代码检查:
echo   npm run lint
echo.

pause
