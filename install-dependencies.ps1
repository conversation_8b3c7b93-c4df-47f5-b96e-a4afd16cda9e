# 交互绘本项目依赖安装脚本 (PowerShell)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🚀 交互绘本项目依赖安装脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查 Node.js 和 npm
Write-Host "📋 检查 Node.js 和 npm 版本..." -ForegroundColor Yellow

try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js 版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安装或未添加到 PATH" -ForegroundColor Red
    Write-Host "请先安装 Node.js: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

try {
    $npmVersion = npm --version
    Write-Host "✅ npm 版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm 未安装或未添加到 PATH" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""

# 清理旧依赖
Write-Host "📦 清理旧的依赖..." -ForegroundColor Yellow

if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force "node_modules"
    Write-Host "✅ 已清理旧的 node_modules" -ForegroundColor Green
}

if (Test-Path "package-lock.json") {
    Remove-Item "package-lock.json"
    Write-Host "✅ 已清理旧的 package-lock.json" -ForegroundColor Green
}

Write-Host ""

# 检查网络连接
Write-Host "🌐 检查网络连接..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://registry.npmjs.org/" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ 网络连接正常" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 网络连接可能有问题，建议使用国内镜像" -ForegroundColor Yellow
    $useChineseMirror = Read-Host "是否使用淘宝镜像？(y/n)"
    if ($useChineseMirror -eq "y" -or $useChineseMirror -eq "Y") {
        npm config set registry https://registry.npmmirror.com
        Write-Host "✅ 已设置淘宝镜像" -ForegroundColor Green
    }
}

Write-Host ""

# 安装依赖
Write-Host "📥 安装项目依赖..." -ForegroundColor Yellow
Write-Host "这可能需要几分钟时间，请耐心等待..." -ForegroundColor Cyan

try {
    npm install
    Write-Host "✅ 依赖安装完成！" -ForegroundColor Green
} catch {
    Write-Host "❌ 依赖安装失败" -ForegroundColor Red
    Write-Host "请检查网络连接或尝试手动运行: npm install" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""

# 验证安装
Write-Host "🔧 验证安装..." -ForegroundColor Yellow
npm list --depth=0

Write-Host ""
Write-Host "🎉 安装完成！" -ForegroundColor Green
Write-Host ""
Write-Host "现在您可以运行以下命令：" -ForegroundColor Cyan
Write-Host ""
Write-Host "  启动开发服务器:" -ForegroundColor White
Write-Host "  npm run dev" -ForegroundColor Yellow
Write-Host ""
Write-Host "  构建生产版本:" -ForegroundColor White
Write-Host "  npm run build" -ForegroundColor Yellow
Write-Host ""
Write-Host "  运行代码检查:" -ForegroundColor White
Write-Host "  npm run lint" -ForegroundColor Yellow
Write-Host ""

Read-Host "按任意键退出"
