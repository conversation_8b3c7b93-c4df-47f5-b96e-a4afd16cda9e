# 📁 Setup 文件夹

这个文件夹包含了交互绘本项目的所有安装和配置相关文件。

## 📋 文件列表

### 🔧 安装脚本
- **`install-dependencies.bat`** - Windows 批处理安装脚本
- **`install-dependencies.ps1`** - PowerShell 安装脚本
- **`install-dependencies.sh`** - Linux/macOS Shell 安装脚本

### 📖 文档
- **`INSTALLATION_GUIDE.md`** - 详细安装指南
- **`QUICK_START.md`** - 快速启动指南
- **`README.md`** - 本文件

## 🚀 快速开始

### Windows 用户
1. 确保已安装 Node.js
2. 双击运行 `install-dependencies.bat`
3. 等待安装完成
4. 运行 `npm run dev`

### macOS/Linux 用户
```bash
cd setup
chmod +x install-dependencies.sh
./install-dependencies.sh
cd ..
npm run dev
```

## 📝 脚本功能

所有安装脚本都会自动执行以下操作：

1. ✅ **检查环境** - 验证 Node.js 和 npm 是否已安装
2. ✅ **切换目录** - 自动切换到项目根目录
3. ✅ **清理旧依赖** - 删除旧的 node_modules 和 package-lock.json
4. ✅ **网络检测** - 检查网络连接，可选择使用国内镜像
5. ✅ **安装依赖** - 自动安装所有项目依赖
6. ✅ **验证安装** - 检查安装结果
7. ✅ **显示指引** - 提供下一步操作指导

## 🔍 故障排除

如果安装脚本无法运行：

1. **检查 Node.js 安装**：
   ```bash
   node --version
   npm --version
   ```

2. **手动安装依赖**：
   ```bash
   cd ..  # 回到项目根目录
   npm install
   ```

3. **使用国内镜像**：
   ```bash
   npm config set registry https://registry.npmmirror.com
   npm install
   ```

## 📞 获取帮助

- 查看 [详细安装指南](./INSTALLATION_GUIDE.md)
- 查看 [快速启动指南](./QUICK_START.md)
- 查看 [项目文档](../docs/README.md)

---

**提示**: 所有脚本都设计为从 setup 文件夹内运行，会自动处理路径问题。
